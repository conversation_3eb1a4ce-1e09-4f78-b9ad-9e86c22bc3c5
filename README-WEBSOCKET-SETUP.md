# Laravel + Node.js Socket.IO WebSocket Setup

This project implements real-time chat functionality using <PERSON><PERSON> with Node.js (Socket.IO) as a WebSocket server, with Redis as a bridge for communication.

## Architecture Overview

```
Laravel App → Redis → Node.js Socket.IO Server → WebSocket Clients
```

1. **<PERSON>vel**: Handles HTTP requests and broadcasts events to Redis
2. **Redis**: Acts as a message broker between Laravel and Node.js
3. **Node.js Socket.IO**: WebSocket server that listens to Redis and broadcasts to clients
4. **Frontend**: Uses Laravel Echo + Socket.IO client to receive real-time updates

## Setup Complete ✅

### What's Been Implemented

1. **Laravel Broadcasting Configuration**
   - Created `config/broadcasting.php` with Redis driver
   - Updated `.env` to use Redis for broadcasting
   - Fixed `ChatController` and `MessageSent` event

2. **Node.js Socket.IO Server**
   - Full Redis integration: `socket-server.js` (requires Redis)
   - Simple version for testing: `socket-server-simple.js` (works without Redis)
   - CORS configuration for Laravel integration
   - Health check endpoint

3. **Frontend Integration**
   - Updated `resources/js/bootstrap.js` with Laravel Echo + Socket.IO
   - Created chat interface at `/chat` route
   - Real-time message display and sending

4. **Package Dependencies**
   - Installed: `socket.io`, `redis`, `laravel-echo`, `socket.io-client`, `express`
   - Updated package.json with server scripts

## Current Status

### ✅ Working Components
- Socket.IO server running on port 3001
- Laravel server running on port 8000
- Chat interface accessible at http://127.0.0.1:8000/chat
- WebSocket connections established
- Direct Socket.IO messaging working

### ⚠️ Pending: Redis Integration
Redis is not currently running, so Laravel broadcasting to Redis → Node.js is not active.

## How to Run

### 1. Start Socket.IO Server
```bash
npm run socket-server-simple
```

### 2. Start Laravel Server
```bash
php artisan serve
```

### 3. Access Chat Interface
Open: http://127.0.0.1:8000/chat

## Testing the Setup

1. **Direct Socket.IO Testing**: 
   - Open multiple browser tabs to http://127.0.0.1:8000/chat
   - Type messages and see them appear in real-time across tabs

2. **Laravel Broadcasting Testing** (requires Redis):
   - Start Redis server
   - Use `npm run socket-server` (full version)
   - Send messages via Laravel routes

## File Structure

```
├── socket-server.js              # Full server with Redis integration
├── socket-server-simple.js       # Simple server for testing
├── config/broadcasting.php        # Laravel broadcasting config
├── app/Events/MessageSent.php     # Laravel broadcast event
├── app/Http/Controllers/ChatController.php  # Chat controller
├── resources/views/chat.blade.php # Chat interface
├── resources/js/bootstrap.js      # Laravel Echo configuration
└── routes/web.php                 # Chat routes
```

## Next Steps

### To Enable Full Laravel → Redis → Socket.IO Flow:

1. **Install and Start Redis**:
   ```bash
   # Using Docker
   docker run -d -p 6379:6379 redis:alpine
   
   # Or install Redis locally
   ```

2. **Switch to Full Socket.IO Server**:
   ```bash
   npm run socket-server
   ```

3. **Test Laravel Broadcasting**:
   - Send POST request to `/chat/send` with message
   - Should appear in all connected WebSocket clients

### Additional Enhancements:
- User authentication integration
- Message persistence to database
- Private channels and rooms
- File upload support
- Typing indicators
- Online user presence

## Troubleshooting

### Port Conflicts
- Socket.IO server: Change port in `socket-server-simple.js` and update frontend URLs
- Laravel server: Use `php artisan serve --port=8080`

### CORS Issues
- Update CORS origins in Socket.IO server configuration
- Ensure frontend URLs match server configuration

### Redis Connection
- Verify Redis is running: `redis-cli ping`
- Check Redis configuration in `.env` file
- Ensure Redis host/port match in both Laravel and Node.js

## Success! 🎉

The basic WebSocket setup is now working. You can:
- Send and receive real-time messages
- Connect multiple clients
- See live connection status
- Use the foundation for more advanced features

The architecture is ready for Redis integration when needed for full Laravel broadcasting support.
