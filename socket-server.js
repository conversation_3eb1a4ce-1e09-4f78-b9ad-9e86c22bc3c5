import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { createClient } from 'redis';

// Create Express app and HTTP server
const app = express();
const server = createServer(app);

// Configure Socket.IO with CORS
const io = new Server(server, {
    cors: {
        origin: ["http://localhost", "http://127.0.0.1", "http://localhost:8000", "http://127.0.0.1:8000"],
        methods: ["GET", "POST"],
        credentials: true
    }
});

// Create Redis client
const redisClient = createClient({
    url: 'redis://127.0.0.1:6379'
});

// Create Redis subscriber (separate connection for pub/sub)
const redisSubscriber = createClient({
    url: 'redis://127.0.0.1:6379'
});

// Redis connection flag
let redisConnected = false;

// Handle Redis connection
redisClient.on('connect', () => {
    console.log('Connected to Redis');
    redisConnected = true;
});

redisClient.on('error', (err) => {
    console.error('Redis Client Error:', err);
    redisConnected = false;
});

redisSubscriber.on('connect', () => {
    console.log('Redis Subscriber connected');
});

redisSubscriber.on('error', (err) => {
    console.error('Redis Subscriber Error:', err);
});

// Connect to Redis
async function connectRedis() {
    try {
        await redisClient.connect();
        await redisSubscriber.connect();
        console.log('Redis connections established');
        redisConnected = true;
    } catch (error) {
        console.error('Failed to connect to Redis:', error);
        console.log('Continuing without Redis - Laravel broadcasting will not work');
        redisConnected = false;
    }
}

// Handle Socket.IO connections
io.on('connection', (socket) => {
    console.log('New client connected:', socket.id);

    // Join the chat channel
    socket.join('chat');
    
    // Handle client disconnect
    socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
    });

    // Handle manual message sending (optional, for testing)
    socket.on('send-message', (data) => {
        console.log('Message received from client:', data);
        // Broadcast to all clients in the chat channel
        io.to('chat').emit('message-received', data);
    });
});

// Subscribe to Laravel broadcast events
async function subscribeToLaravelEvents() {
    if (!redisConnected) {
        console.log('Redis not connected - skipping Laravel event subscription');
        return;
    }

    try {
        // Subscribe to Laravel's broadcasting channel
        // Laravel uses a specific pattern for Redis keys
        await redisSubscriber.subscribe('laravel_database_chat');

        redisSubscriber.on('message', (channel, message) => {
            console.log('Received message from Laravel:', { channel, message });

            try {
                // Parse the Laravel broadcast message
                const data = JSON.parse(message);

                // Extract the actual event data
                if (data.event && data.data) {
                    console.log('Broadcasting to clients:', data.data);

                    // Broadcast to all connected clients in the chat channel
                    io.to('chat').emit('message-received', {
                        message: data.data.message,
                        timestamp: new Date().toISOString()
                    });
                }
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        });

        console.log('Subscribed to Laravel broadcast events');
    } catch (error) {
        console.error('Failed to subscribe to Redis:', error);
    }
}

// Start the server
const PORT = process.env.PORT || 3000;

async function startServer() {
    await connectRedis();
    await subscribeToLaravelEvents();
    
    server.listen(PORT, () => {
        console.log(`Socket.IO server running on port ${PORT}`);
        console.log(`WebSocket endpoint: ws://localhost:${PORT}`);
    });
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    if (redisConnected) {
        try {
            await redisClient.quit();
            await redisSubscriber.quit();
        } catch (error) {
            console.error('Error closing Redis connections:', error);
        }
    }
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});

// Start the application
startServer().catch(console.error);
