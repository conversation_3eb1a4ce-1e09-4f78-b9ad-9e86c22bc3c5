{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "socket-server": "node socket-server.js", "socket-server-simple": "node socket-server-simple.js", "dev-all": "concurrently \"npm run dev\" \"npm run socket-server-simple\""}, "devDependencies": {"@popperjs/core": "^2.11.6", "@tailwindcss/vite": "^4.0.0", "axios": "^1.8.2", "bootstrap": "^5.2.3", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "sass": "^1.56.1", "tailwindcss": "^4.0.0", "vite": "^6.2.4"}, "dependencies": {"express": "^5.1.0", "laravel-echo": "^2.1.6", "redis": "^5.5.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}}