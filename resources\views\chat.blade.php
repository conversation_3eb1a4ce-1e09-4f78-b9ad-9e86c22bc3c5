@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Chat Room') }}</div>

                <div class="card-body">
                    <div id="chat-messages" class="mb-3" style="height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                        <!-- Messages will appear here -->
                    </div>
                    
                    <div class="input-group">
                        <input type="text" id="message-input" class="form-control" placeholder="Type your message..." maxlength="255">
                        <button class="btn btn-primary" type="button" id="send-button">Send</button>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <span id="connection-status" class="badge bg-secondary">Connecting...</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('chat-messages');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const connectionStatus = document.getElementById('connection-status');
    
    // Function to add message to chat
    function addMessage(message, isOwn = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `mb-2 ${isOwn ? 'text-end' : ''}`;
        
        const timestamp = new Date().toLocaleTimeString();
        messageDiv.innerHTML = `
            <div class="d-inline-block p-2 rounded ${isOwn ? 'bg-primary text-white' : 'bg-light'}">
                <div>${message}</div>
                <small class="opacity-75">${timestamp}</small>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Function to send message
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message === '') return;
        
        // Add message to UI immediately
        addMessage(message, true);
        
        // Send to Laravel backend
        fetch('/chat/send', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            console.log('Message sent:', data);
        })
        .catch(error => {
            console.error('Error sending message:', error);
        });
        
        messageInput.value = '';
    }
    
    // Event listeners
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    // Laravel Echo setup
    if (window.Echo) {
        // Listen for Laravel broadcast events
        window.Echo.channel('chat')
            .listen('MessageSent', (e) => {
                console.log('Message received via Laravel Echo:', e);
                addMessage(e.message, false);
            });
            
        connectionStatus.textContent = 'Connected via Laravel Echo';
        connectionStatus.className = 'badge bg-success';
    }
    
    // Direct Socket.IO connection as fallback/additional
    if (window.io) {
        const socket = window.io('http://localhost:3001');
        
        socket.on('connect', () => {
            console.log('Connected to Socket.IO server');
            connectionStatus.textContent = 'Connected to Socket.IO';
            connectionStatus.className = 'badge bg-success';
        });
        
        socket.on('disconnect', () => {
            console.log('Disconnected from Socket.IO server');
            connectionStatus.textContent = 'Disconnected';
            connectionStatus.className = 'badge bg-danger';
        });
        
        socket.on('message-received', (data) => {
            console.log('Message received via Socket.IO:', data);
            addMessage(data.message, false);
        });
        
        // Optional: Send messages directly via Socket.IO for testing
        window.sendDirectMessage = function(message) {
            socket.emit('send-message', { message: message });
        };
    }
    
    // Add initial welcome message
    addMessage('Welcome to the chat room! Start typing to send messages.', false);
});
</script>
@endsection
